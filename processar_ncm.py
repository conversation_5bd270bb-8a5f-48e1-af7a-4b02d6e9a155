import pandas as pd

# Define o nome do arquivo de entrada e de saída
input_filename = "CPESC_ITEMFISCAL_202508261723-completo2.csv"
output_filename = "CPESC_ITEMFISCAL_final_com_nivel_anterior_e_descricao_combinada.csv"

# Tenta carregar o arquivo CSV, tratando todas as colunas como strings, conforme solicitado.
try:
    df = pd.read_csv(input_filename, dtype=str)
except FileNotFoundError:
    print(f"Erro: O arquivo '{input_filename}' não foi encontrado.")
    print("Por favor, certifique-se de que o script está na mesma pasta que o arquivo CSV.")
    exit()

# Cria um conjunto (set) com todos os NCMs únicos para uma busca muito rápida.
# O .dropna() remove valores vazios que possam causar problemas.
all_ncms = set(df['NCM'].dropna())

def encontrar_pai_otimizado(ncm, ncms_set):
    """
    Encontra o NCM pai para um dado NCM verificando seus prefixos.
    Esta abordagem é muito mais rápida do que comparar com todos os outros NCMs.
    """
    # Garante que o ncm seja uma string para poder fatiar (slice)
    ncm = str(ncm)
    
    # Itera do prefixo mais longo possível para o mais curto
    for i in range(len(ncm) - 1, 0, -1):
        prefixo = ncm[:i]
        if prefixo in ncms_set:
            # O primeiro que encontrarmos será o mais longo, portanto, é o pai.
            return prefixo
    
    # Se nenhum prefixo for encontrado no conjunto, não há pai.
    return ""

# Aplica a função otimizada para criar a nova coluna "NIVEL_ANTERIOR"
print("Processando a tabela... Isso pode levar alguns segundos.")
df['NIVEL_ANTERIOR'] = df['NCM'].apply(lambda ncm: encontrar_pai_otimizado(ncm, all_ncms))

# Cria um dicionário para mapear NCM -> Descrição para busca rápida
ncm_to_descricao = dict(zip(df['NCM'], df['DESCRICAO']))

def precisa_concatenar(descricao):
    """
    Verifica se uma descrição precisa ser concatenada baseada nas regras:
    - Contém 'OUTR' nas primeiras 7 letras, OU
    - Começa com '-' ou '--'
    """
    if not descricao:
        return False

    descricao = str(descricao)
    primeiros_7_chars = descricao[:7].upper() if len(descricao) >= 7 else descricao.upper()

    return 'OUTR' in primeiros_7_chars or descricao.startswith('-') or descricao.startswith('--')

def limpar_descricao(descricao):
    """
    Limpa a descrição removendo pontos, dois-pontos, traços e espaços extras.
    """
    return descricao.replace('.', '').replace(':', '').replace('--', '').replace('-', '').strip()

def criar_descricao_combinada(row):
    """
    Cria uma nova descrição baseada nas regras recursivas:
    - Se a descrição contém 'OUTR' nas primeiras 7 letras, OU
    - Se a descrição começa com '-' ou '--',
      verifica o pai e recursivamente o avô se necessário
    - Concatena: {avô} -> {pai} -> {atual} conforme necessário
    """
    descricao_atual = str(row['DESCRICAO']) if pd.notna(row['DESCRICAO']) else ""
    nivel_anterior = str(row['NIVEL_ANTERIOR']) if pd.notna(row['NIVEL_ANTERIOR']) else ""

    # Se a descrição atual não precisa concatenar, retorna ela mesma
    if not precisa_concatenar(descricao_atual):
        return limpar_descricao(descricao_atual)

    # Se não há pai, retorna a descrição atual
    if not nivel_anterior or nivel_anterior not in ncm_to_descricao:
        return descricao_limpa

    # Pega a descrição do pai
    descricao_pai = str(ncm_to_descricao[nivel_anterior])

    # Verifica se o pai também precisa concatenar (buscar o avô)
    if precisa_concatenar(descricao_pai):
        # Encontra o avô (pai do pai)
        ncm_avo = encontrar_pai_otimizado(nivel_anterior, all_ncms)

        if ncm_avo and ncm_avo in ncm_to_descricao:
            descricao_avo = str(ncm_to_descricao[ncm_avo])
            descricao_concat = "{descricao_avo} -> {descricao_pai} -> {descricao_atual}"
            descricao_limpa = descricao_concat.replace('.', '').replace(':', '').replace('--', '').replace('-', '').strip()
            return descricao_limpa
        else:
            descricao_concat = "{descricao_pai} -> {descricao_atual}"
            descricao_limpa = descricao_concat.replace('.', '').replace(':', '').replace('--', '').replace('-', '').strip()
            return descricao_limpa
    else:
        descricao_concat = "{descricao_pai} -> {descricao_atual}"
        descricao_limpa = descricao_concat.replace('.', '').replace(':', '').replace('--', '').replace('-', '').strip()
        return descricao_limpa

# Aplica a função para criar a nova coluna de descrição
print("Criando nova coluna de descrição...")

df['DESCRICAO_NOVA'] = df.apply(criar_descricao_combinada, axis=1)

# Salva o DataFrame resultante em um novo arquivo CSV
df.to_csv(output_filename, index=False)

print("Processamento concluído com sucesso!")
print(f"O novo arquivo '{output_filename}' foi criado com as colunas 'NIVEL_ANTERIOR' e 'DESCRICAO_NOVA'.")