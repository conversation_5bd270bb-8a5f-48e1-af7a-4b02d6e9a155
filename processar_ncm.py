import pandas as pd

# Define o nome do arquivo de entrada e de saída
input_filename = "CPESC_ITEMFISCAL_202508261723-completo2.csv"
output_filename = "CPESC_ITEMFISCAL_final_com_nivel_anterior_e_descricao_combinada.csv"

# Tenta carregar o arquivo CSV, tratando todas as colunas como strings, conforme solicitado.
try:
    df = pd.read_csv(input_filename, dtype=str)
except FileNotFoundError:
    print(f"Erro: O arquivo '{input_filename}' não foi encontrado.")
    print("Por favor, certifique-se de que o script está na mesma pasta que o arquivo CSV.")
    exit()

# Cria um conjunto (set) com todos os NCMs únicos para uma busca muito rápida.
# O .dropna() remove valores vazios que possam causar problemas.
all_ncms = set(df['NCM'].dropna())

def encontrar_pai_otimizado(ncm, ncms_set):
    """
    Encontra o NCM pai para um dado NCM verificando seus prefixos.
    Esta abordagem é muito mais rápida do que comparar com todos os outros NCMs.
    """
    # Garante que o ncm seja uma string para poder fatiar (slice)
    ncm = str(ncm)
    
    # Itera do prefixo mais longo possível para o mais curto
    for i in range(len(ncm) - 1, 0, -1):
        prefixo = ncm[:i]
        if prefixo in ncms_set:
            # O primeiro que encontrarmos será o mais longo, portanto, é o pai.
            return prefixo
    
    # Se nenhum prefixo for encontrado no conjunto, não há pai.
    return ""

# Aplica a função otimizada para criar a nova coluna "NIVEL_ANTERIOR"
print("Processando a tabela... Isso pode levar alguns segundos.")
df['NIVEL_ANTERIOR'] = df['NCM'].apply(lambda ncm: encontrar_pai_otimizado(ncm, all_ncms))

# Cria um dicionário para mapear NCM -> Descrição para busca rápida
ncm_to_descricao = dict(zip(df['NCM'], df['DESCRICAO']))

def criar_descricao_combinada(row):
    """
    Cria uma nova descrição baseada nas regras:
    - Se a descrição contém 'OUTR' nas primeiras 7 letras, OU
    - Se a descrição começa com '-' ou '--',
      combina descrição do pai + descrição atual
    - Senão, mantém a descrição original
    """
    descricao_atual = str(row['DESCRICAO']) if pd.notna(row['DESCRICAO']) else ""
    nivel_anterior = str(row['NIVEL_ANTERIOR']) if pd.notna(row['NIVEL_ANTERIOR']) else ""

    # Verifica se a descrição contém 'OUTR' nas primeiras 7 caracteres
    primeiros_7_chars = descricao_atual[:7].upper() if len(descricao_atual) >= 7 else descricao_atual.upper()

    if 'OUTR' in primeiros_7_chars or descricao_atual.startswith('-') or descricao_atual.startswith('--'):
        # Se há um nível anterior, busca sua descrição
        if nivel_anterior and nivel_anterior in ncm_to_descricao:
            descricao_pai = str(ncm_to_descricao[nivel_anterior])
            primeiros_7_chars = descricao_pai[:7].upper() if len(descricao_pai) >= 7 else descricao_pai.upper()
            if 'OUTR' in primeiros_7_chars or descricao_pai.startswith('-') or descricao_pai.startswith('--'):
                descricao_vo = str(ncm_to_descricao[nivel_anterior ])
            descricao_pai_limpa = descricao_pai.replace('--', '').replace('-', '').strip()
            return f"{descricao_pai_limpa} -> {descricao_atual}"
        else:
            # Se não há pai, mantém a descrição original
            return descricao_atual
    else:
        # Se não atende aos critérios, mantém a descrição original
        return descricao_atual

# Aplica a função para criar a nova coluna de descrição
print("Criando nova coluna de descrição...")
df['DESCRICAO_NOVA'] = df.apply(criar_descricao_combinada, axis=1)

# Salva o DataFrame resultante em um novo arquivo CSV
df.to_csv(output_filename, index=False)

print("Processamento concluído com sucesso!")
print(f"O novo arquivo '{output_filename}' foi criado com as colunas 'NIVEL_ANTERIOR' e 'DESCRICAO_NOVA'.")