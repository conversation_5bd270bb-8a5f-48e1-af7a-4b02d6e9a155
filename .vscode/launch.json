{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Python: processar_ncm.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/processar_ncm.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Python: Current File (External Terminal)",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "externalTerminal",
            "justMyCode": true
        },
        {
            "name": "Python: Debug with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true,
            "args": []
        },
        {
            "name": "Python: Module",
            "type": "debugpy",
            "request": "launch",
            "module": "enter-your-module-name-here",
            "console": "integratedTerminal",
            "justMyCode": true
        }
    ]
}